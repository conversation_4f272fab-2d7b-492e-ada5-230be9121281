#!/usr/bin/env python3
"""
اختبار مبسط للوكيل الذكي
"""
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_components():
    """اختبار المكونات الأساسية"""
    
    print("🧪 اختبار المكونات الأساسية")
    print("=" * 50)
    
    # اختبار محلل المهام
    try:
        from task_analyzer import TaskAnalyzer
        analyzer = TaskAnalyzer()
        analysis = analyzer.analyze_task("اكتب برنامج بسيط لحساب مجموع رقمين")
        print("✅ محلل المهام يعمل")
        print(f"   نوع المهمة: {analysis.task_type}")
        print(f"   التعقيد: {analysis.complexity}")
        print(f"   عدد الخطوات: {analysis.estimated_steps}")
    except Exception as e:
        print(f"❌ محلل المهام: {e}")
    
    # اختبار مولد الكود
    try:
        from code_generator import CodeGenerator
        from task_analyzer import TaskAnalysis, TaskStep
        
        generator = CodeGenerator()
        
        # إنشاء تحليل وهمي
        dummy_analysis = TaskAnalysis(
            task_type="math_calculation",
            complexity="simple",
            estimated_steps=2,
            estimated_time=60,
            steps=[],
            required_libraries=[],
            success_criteria=[]
        )
        
        generated = generator.generate_code("حساب مجموع رقمين", dummy_analysis)
        print("✅ مولد الكود يعمل")
        print(f"   الملف: {generated.filename}")
        print(f"   عدد الدوال: {len(generated.functions)}")
    except Exception as e:
        print(f"❌ مولد الكود: {e}")
    
    # اختبار مختبر الكود
    try:
        from code_tester import CodeTester
        from code_generator import GeneratedCode
        
        tester = CodeTester()
        
        # إنشاء كود وهمي للاختبار
        dummy_code = GeneratedCode(
            code='def add(a, b):\n    return a + b\n\nif __name__ == "__main__":\n    print(add(2, 3))',
            filename="test.py",
            description="اختبار",
            imports=[],
            functions=["add"],
            classes=[],
            test_cases=[]
        )
        
        report = tester.test_code(dummy_code)
        print("✅ مختبر الكود يعمل")
        print(f"   الاختبارات الناجحة: {report.passed_tests}/{report.total_tests}")
    except Exception as e:
        print(f"❌ مختبر الكود: {e}")
    
    print("\n" + "=" * 50)

def test_simple_agent():
    """اختبار الوكيل بمهمة بسيطة"""
    
    print("🤖 اختبار الوكيل مع مهمة بسيطة")
    print("=" * 50)
    
    try:
        from intelligent_agent import IntelligentCodingAgent
        
        # إنشاء الوكيل
        agent = IntelligentCodingAgent()
        print("✅ تم إنشاء الوكيل بنجاح")
        
        # مهمة بسيطة جداً
        task = "اكتب دالة لجمع رقمين"
        print(f"📝 المهمة: {task}")
        
        # حل المهمة مع تقييد التكرارات
        result = agent.solve_problem(task, max_iterations=1)
        
        print(f"📊 النتيجة: {'✅ نجح' if result.success else '⚠️ يحتاج تحسين'}")
        print(f"📁 الملف: {result.generated_code.filename}")
        
        if result.test_report:
            print(f"🧪 الاختبارات: {result.test_report.passed_tests}/{result.test_report.total_tests}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ خطأ في الوكيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار مبسط للوكيل الذكي")
    print("=" * 60)
    
    # اختبار المكونات
    test_components()
    
    # اختبار الوكيل
    success = test_simple_agent()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if success:
        print("🎉 الاختبار نجح! الوكيل جاهز للاستخدام")
        print("\n💡 يمكنك الآن تجربة:")
        print("  python main.py 'اكتب برنامج لحساب الأعداد الأولية'")
        print("  python main.py --interactive")
    else:
        print("⚠️ الاختبار يحتاج مراجعة")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
