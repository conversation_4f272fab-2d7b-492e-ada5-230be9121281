#!/usr/bin/env python3
"""
تشغيل مبسط للوكيل الذكي
"""
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_agent import IntelligentCodingAgent

def main():
    """تشغيل الوكيل مع مهمة محددة"""
    
    print("🤖 الوكيل الذكي لحل المشاكل البرمجية")
    print("=" * 60)
    
    # إنشاء الوكيل
    try:
        agent = IntelligentCodingAgent()
        print("✅ تم إنشاء الوكيل بنجاح")
    except Exception as e:
        print(f"❌ فشل في إنشاء الوكيل: {e}")
        return
    
    # الحصول على المهمة من المستخدم
    if len(sys.argv) > 1:
        task = " ".join(sys.argv[1:])
    else:
        task = input("\n📝 أدخل المهمة البرمجية: ").strip()
    
    if not task:
        print("❌ لم يتم تحديد مهمة")
        return
    
    print(f"\n🎯 المهمة: {task}")
    print("-" * 60)
    
    # حل المهمة
    try:
        result = agent.solve_problem(task, max_iterations=2)
        
        # عرض النتيجة
        print(f"\n🏆 النتيجة: {'✅ نجح' if result.success else '⚠️ يحتاج تحسين'}")
        
        if result.success:
            print(f"📁 الملف المحفوظ: output/{result.generated_code.filename}")
            print("🎉 يمكنك الآن تشغيل الكود!")
            
            # عرض جزء من الكود
            if result.final_code:
                print(f"\n📄 عينة من الكود:")
                lines = result.final_code.split('\n')[:15]
                for i, line in enumerate(lines, 1):
                    print(f"  {i:2d}: {line}")
                if len(result.final_code.split('\n')) > 15:
                    print("  ...")
        else:
            print("💡 راجع التوصيات في التقرير أعلاه")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إيقاف العملية")
    except Exception as e:
        print(f"\n❌ خطأ في تنفيذ المهمة: {e}")

if __name__ == "__main__":
    main()
