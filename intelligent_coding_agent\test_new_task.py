#!/usr/bin/env python3
"""
اختبار مهمة جديدة مع الوكيل الذكي
"""
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_agent import IntelligentCodingAgent

def test_new_task():
    """اختبار مهمة جديدة"""
    
    print("🤖 اختبار مهمة جديدة مع الوكيل الذكي")
    print("=" * 60)
    
    # إنشاء الوكيل
    agent = IntelligentCodingAgent()
    
    # مهام مختلفة للاختبار
    tasks = [
        "اكتب برنامج لحساب مضروب العدد",
        "برنامج لترتيب قائمة من الأرقام بطريقة الفقاعات", 
        "حاسبة بسيطة للعمليات الأساسية",
        "برنامج لإيجاد أكبر وأصغر رقم في قائمة",
        "دالة لفحص ما إذا كان النص palindrome"
    ]
    
    print("📋 المهام المتاحة:")
    for i, task in enumerate(tasks, 1):
        print(f"  {i}. {task}")
    
    # اختيار المهمة
    try:
        choice = int(input(f"\n🎯 اختر رقم المهمة (1-{len(tasks)}): "))
        if 1 <= choice <= len(tasks):
            selected_task = tasks[choice - 1]
        else:
            print("❌ رقم غير صحيح، سيتم اختيار المهمة الأولى")
            selected_task = tasks[0]
    except:
        print("❌ إدخال غير صحيح، سيتم اختيار المهمة الأولى")
        selected_task = tasks[0]
    
    print(f"\n🎯 المهمة المختارة: {selected_task}")
    print("-" * 60)
    
    # حل المهمة
    try:
        result = agent.solve_problem(selected_task, max_iterations=3)
        
        # عرض النتيجة
        print(f"\n🏆 النتيجة: {'✅ نجح' if result.success else '⚠️ يحتاج تحسين'}")
        print(f"📁 الملف: output/{result.generated_code.filename}")
        print(f"⏱️ الوقت: {result.execution_time:.1f} ثانية")
        
        if result.test_report:
            print(f"🧪 الاختبارات: {result.test_report.passed_tests}/{result.test_report.total_tests} نجح")
        
        # عرض الكود المولد
        if result.final_code:
            print(f"\n📄 الكود المولد:")
            print("=" * 40)
            print(result.final_code[:500] + "..." if len(result.final_code) > 500 else result.final_code)
            print("=" * 40)
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def run_generated_code():
    """تشغيل الكود المولد"""
    
    print("\n🚀 تشغيل الكود المولد")
    print("-" * 30)
    
    # البحث عن آخر ملف تم إنشاؤه
    output_dir = "output"
    if os.path.exists(output_dir):
        files = [f for f in os.listdir(output_dir) if f.endswith('.py')]
        if files:
            # ترتيب الملفات حسب تاريخ التعديل
            files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), reverse=True)
            latest_file = files[0]
            
            print(f"📁 تشغيل الملف: {latest_file}")
            
            # تشغيل الملف
            import subprocess
            try:
                result = subprocess.run(
                    [sys.executable, os.path.join(output_dir, latest_file)],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                print("📤 المخرجات:")
                print(result.stdout)
                
                if result.stderr:
                    print("⚠️ الأخطاء:")
                    print(result.stderr)
                    
            except subprocess.TimeoutExpired:
                print("⏰ انتهت مهلة التنفيذ")
            except Exception as e:
                print(f"❌ خطأ في التنفيذ: {e}")
        else:
            print("❌ لا توجد ملفات Python في مجلد output")
    else:
        print("❌ مجلد output غير موجود")

if __name__ == "__main__":
    # اختبار مهمة جديدة
    result = test_new_task()
    
    if result and result.success:
        # تشغيل الكود المولد
        run_generated_code()
        
        print(f"\n🎉 تم إنجاز المهمة بنجاح!")
        print(f"📁 يمكنك العثور على الكود في: output/{result.generated_code.filename}")
    else:
        print(f"\n⚠️ المهمة تحتاج تحسينات إضافية")
