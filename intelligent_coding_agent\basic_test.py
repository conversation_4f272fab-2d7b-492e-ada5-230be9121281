#!/usr/bin/env python3
"""
اختبار أساسي جداً
"""

print("🧪 اختبار أساسي للمشروع")
print("=" * 40)

# اختبار الاستيراد
try:
    print("📦 اختبار الاستيراد...")
    
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    print("✅ تم تحميل المسار")
    
    from config import Config
    print("✅ تم استيراد Config")
    
    from task_analyzer import TaskAnalyzer
    print("✅ تم استيراد TaskAnalyzer")
    
    from code_generator import CodeGenerator  
    print("✅ تم استيراد CodeGenerator")
    
    from code_tester import CodeTester
    print("✅ تم استيراد CodeTester")
    
    from intelligent_agent import IntelligentCodingAgent
    print("✅ تم استيراد IntelligentCodingAgent")
    
    print("\n🎉 جميع الاستيرادات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    import traceback
    traceback.print_exc()

print("=" * 40)
