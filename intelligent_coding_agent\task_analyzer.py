"""
محلل المهام الذكي - يحدد نوع المهمة والخطوات المطلوبة
"""
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from config import Config

@dataclass
class TaskStep:
    """خطوة واحدة في المهمة"""
    id: int
    description: str
    type: str  # 'analysis', 'coding', 'testing', 'optimization'
    priority: int
    estimated_time: int  # بالثواني
    dependencies: List[int] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

@dataclass
class TaskAnalysis:
    """تحليل شامل للمهمة"""
    task_type: str
    complexity: str  # 'simple', 'medium', 'complex'
    estimated_steps: int
    estimated_time: int  # بالثواني
    steps: List[TaskStep]
    required_libraries: List[str]
    success_criteria: List[str]

class TaskAnalyzer:
    """محلل المهام الذكي"""
    
    def __init__(self):
        self.task_patterns = {
            "algorithm": [
                r"خوارزمية|algorithm",
                r"ترتيب|sort|sorting",
                r"بحث|search|searching",
                r"أعداد أولية|prime numbers",
                r"فيبوناتشي|fibonacci",
            ],
            "data_structure": [
                r"قائمة|list|array",
                r"مكدس|stack",
                r"طابور|queue",
                r"شجرة|tree",
                r"جراف|graph",
            ],
            "web_scraping": [
                r"استخراج|scraping|scrape",
                r"موقع|website|web",
                r"بيانات من الإنترنت|web data",
            ],
            "file_processing": [
                r"ملف|file|files",
                r"قراءة|read|reading",
                r"كتابة|write|writing",
                r"معالجة ملفات|file processing",
            ],
            "math_calculation": [
                r"حساب|calculate|calculation",
                r"رياضيات|math|mathematics",
                r"معادلة|equation",
                r"مجموع|sum|addition",
                r"ضرب|multiply|multiplication",
            ],
            "gui": [
                r"واجهة|interface|gui",
                r"نافذة|window",
                r"تطبيق|application|app",
            ],
            "automation": [
                r"أتمتة|automation|automate",
                r"مهمة تلقائية|automatic task",
            ],
        }
    
    def analyze_task(self, task_description: str) -> TaskAnalysis:
        """تحليل المهمة وتحديد الخطوات المطلوبة"""
        
        # تحديد نوع المهمة
        task_type = self._identify_task_type(task_description)
        
        # تحديد مستوى التعقيد
        complexity = self._assess_complexity(task_description)
        
        # تحديد الخطوات المطلوبة
        steps = self._generate_steps(task_description, task_type, complexity)
        
        # تحديد المكتبات المطلوبة
        required_libraries = self._identify_required_libraries(task_description, task_type)
        
        # تحديد معايير النجاح
        success_criteria = self._define_success_criteria(task_description, task_type)
        
        # حساب التقديرات
        estimated_steps = len(steps)
        estimated_time = sum(step.estimated_time for step in steps)
        
        return TaskAnalysis(
            task_type=task_type,
            complexity=complexity,
            estimated_steps=estimated_steps,
            estimated_time=estimated_time,
            steps=steps,
            required_libraries=required_libraries,
            success_criteria=success_criteria
        )
    
    def _identify_task_type(self, description: str) -> str:
        """تحديد نوع المهمة"""
        description_lower = description.lower()
        
        for task_type, patterns in self.task_patterns.items():
            for pattern in patterns:
                if re.search(pattern, description_lower):
                    return task_type
        
        return "general"
    
    def _assess_complexity(self, description: str) -> str:
        """تقييم مستوى تعقيد المهمة"""
        complexity_indicators = {
            "simple": [
                r"بسيط|simple|basic",
                r"حساب|calculate",
                r"طباعة|print",
            ],
            "complex": [
                r"معقد|complex|advanced",
                r"خوارزمية متقدمة|advanced algorithm",
                r"قاعدة بيانات|database",
                r"واجهة|interface|gui",
                r"شبكة|network|api",
            ]
        }
        
        description_lower = description.lower()
        
        # فحص المؤشرات المعقدة أولاً
        for pattern in complexity_indicators["complex"]:
            if re.search(pattern, description_lower):
                return "complex"
        
        # فحص المؤشرات البسيطة
        for pattern in complexity_indicators["simple"]:
            if re.search(pattern, description_lower):
                return "simple"
        
        return "medium"
    
    def _generate_steps(self, description: str, task_type: str, complexity: str) -> List[TaskStep]:
        """توليد خطوات المهمة"""
        steps = []
        step_id = 1
        
        # خطوة التحليل (دائماً موجودة)
        steps.append(TaskStep(
            id=step_id,
            description="تحليل المتطلبات وفهم المشكلة",
            type="analysis",
            priority=1,
            estimated_time=30
        ))
        step_id += 1
        
        # خطوات حسب نوع المهمة
        if task_type in ["algorithm", "math_calculation"]:
            steps.extend([
                TaskStep(
                    id=step_id,
                    description="تصميم الخوارزمية أو المنطق",
                    type="analysis",
                    priority=2,
                    estimated_time=60,
                    dependencies=[1]
                ),
                TaskStep(
                    id=step_id + 1,
                    description="كتابة الكود الأساسي",
                    type="coding",
                    priority=3,
                    estimated_time=120,
                    dependencies=[2]
                ),
            ])
            step_id += 2
        
        elif task_type == "file_processing":
            steps.extend([
                TaskStep(
                    id=step_id,
                    description="تحديد هيكل الملفات والبيانات",
                    type="analysis",
                    priority=2,
                    estimated_time=45,
                    dependencies=[1]
                ),
                TaskStep(
                    id=step_id + 1,
                    description="كتابة دوال القراءة والكتابة",
                    type="coding",
                    priority=3,
                    estimated_time=90,
                    dependencies=[2]
                ),
            ])
            step_id += 2
        
        # خطوة الاختبار (دائماً موجودة)
        steps.append(TaskStep(
            id=step_id,
            description="اختبار الكود والتأكد من صحته",
            type="testing",
            priority=4,
            estimated_time=60,
            dependencies=[step_id - 1]
        ))
        step_id += 1
        
        # خطوة التحسين (للمهام المعقدة)
        if complexity in ["medium", "complex"]:
            steps.append(TaskStep(
                id=step_id,
                description="تحسين الأداء والكود",
                type="optimization",
                priority=5,
                estimated_time=90,
                dependencies=[step_id - 1]
            ))
        
        return steps
    
    def _identify_required_libraries(self, description: str, task_type: str) -> List[str]:
        """تحديد المكتبات المطلوبة"""
        libraries = []
        
        # مكتبات أساسية حسب نوع المهمة
        library_mapping = {
            "web_scraping": ["requests", "beautifulsoup4", "selenium"],
            "data_structure": ["collections"],
            "math_calculation": ["math", "numpy"],
            "file_processing": ["os", "json", "csv"],
            "gui": ["tkinter", "PyQt5"],
            "automation": ["schedule", "selenium"],
        }
        
        if task_type in library_mapping:
            libraries.extend(library_mapping[task_type])
        
        # فحص المكتبات المذكورة في الوصف
        description_lower = description.lower()
        if "pandas" in description_lower:
            libraries.append("pandas")
        if "matplotlib" in description_lower or "رسم" in description_lower:
            libraries.append("matplotlib")
        if "requests" in description_lower or "api" in description_lower:
            libraries.append("requests")
        
        return list(set(libraries))  # إزالة التكرار
    
    def _define_success_criteria(self, description: str, task_type: str) -> List[str]:
        """تحديد معايير النجاح"""
        criteria = [
            "الكود يعمل بدون أخطاء",
            "النتائج صحيحة ومطابقة للمتوقع",
            "الكود منظم وقابل للقراءة",
        ]
        
        # معايير إضافية حسب نوع المهمة
        if task_type == "algorithm":
            criteria.append("الخوارزمية فعالة من ناحية الوقت والذاكرة")
        elif task_type == "file_processing":
            criteria.append("معالجة الملفات تتم بشكل آمن")
        elif task_type == "web_scraping":
            criteria.append("البيانات المستخرجة دقيقة وكاملة")
        
        return criteria

    def print_analysis(self, analysis: TaskAnalysis) -> None:
        """طباعة تحليل المهمة بشكل منسق"""
        print("="*60)
        print("🔍 تحليل المهمة")
        print("="*60)
        print(f"📋 نوع المهمة: {analysis.task_type}")
        print(f"⚡ مستوى التعقيد: {analysis.complexity}")
        print(f"📊 عدد الخطوات المقدر: {analysis.estimated_steps}")
        print(f"⏱️ الوقت المقدر: {analysis.estimated_time // 60} دقيقة")

        if analysis.required_libraries:
            print(f"📦 المكتبات المطلوبة: {', '.join(analysis.required_libraries)}")

        print("\n🗂️ خطوات التنفيذ:")
        for step in analysis.steps:
            deps = f" (يعتمد على: {step.dependencies})" if step.dependencies else ""
            print(f"  {step.id}. {step.description} [{step.type}]{deps}")

        print("\n✅ معايير النجاح:")
        for criterion in analysis.success_criteria:
            print(f"  • {criterion}")
        print("="*60)
