"""
أدوات مساعدة للوكيل الذكي
"""
import os
import json
import time
import re
from typing import Any, Dict, List, Optional
from datetime import datetime

def ensure_directory(path: str) -> None:
    """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
    os.makedirs(path, exist_ok=True)

def save_json(data: Dict[str, Any], filepath: str) -> bool:
    """حفظ البيانات في ملف JSON"""
    try:
        ensure_directory(os.path.dirname(filepath))
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"خطأ في حفظ JSON: {e}")
        return False

def load_json(filepath: str) -> Optional[Dict[str, Any]]:
    """تحميل البيانات من ملف JSON"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"خطأ في تحميل JSON: {e}")
        return None

def format_time(seconds: float) -> str:
    """تنسيق الوقت بشكل قابل للقراءة"""
    if seconds < 60:
        return f"{seconds:.1f} ثانية"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} دقيقة"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} ساعة"

def clean_code(code: str) -> str:
    """تنظيف الكود من الأجزاء غير المرغوب فيها"""
    # إزالة الأسطر الفارغة الزائدة
    lines = code.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # إزالة المسافات الزائدة
        cleaned_line = line.rstrip()
        cleaned_lines.append(cleaned_line)
    
    # إزالة الأسطر الفارغة المتتالية
    final_lines = []
    prev_empty = False
    
    for line in cleaned_lines:
        if line.strip() == "":
            if not prev_empty:
                final_lines.append(line)
            prev_empty = True
        else:
            final_lines.append(line)
            prev_empty = False
    
    return '\n'.join(final_lines)

def extract_functions_from_code(code: str) -> List[str]:
    """استخراج أسماء الدوال من الكود"""
    function_pattern = r'def\s+(\w+)\s*\('
    return re.findall(function_pattern, code)

def extract_imports_from_code(code: str) -> List[str]:
    """استخراج المكتبات المستوردة من الكود"""
    imports = []
    
    # import module
    import_pattern = r'import\s+(\w+)'
    imports.extend(re.findall(import_pattern, code))
    
    # from module import ...
    from_pattern = r'from\s+(\w+)\s+import'
    imports.extend(re.findall(from_pattern, code))
    
    return list(set(imports))  # إزالة التكرار

def validate_python_syntax(code: str) -> tuple[bool, str]:
    """التحقق من صحة صيغة Python"""
    try:
        compile(code, '<string>', 'exec')
        return True, "الصيغة صحيحة"
    except SyntaxError as e:
        return False, f"خطأ في الصيغة: {str(e)}"

def create_backup(filepath: str) -> str:
    """إنشاء نسخة احتياطية من الملف"""
    if not os.path.exists(filepath):
        return ""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{filepath}.backup_{timestamp}"
    
    try:
        import shutil
        shutil.copy2(filepath, backup_path)
        return backup_path
    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return ""

def get_file_stats(filepath: str) -> Dict[str, Any]:
    """الحصول على إحصائيات الملف"""
    if not os.path.exists(filepath):
        return {}
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        return {
            "size_bytes": len(content.encode('utf-8')),
            "lines_total": len(lines),
            "lines_code": len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            "lines_comments": len([line for line in lines if line.strip().startswith('#')]),
            "lines_empty": len([line for line in lines if not line.strip()]),
            "characters": len(content),
            "words": len(content.split()),
        }
    except Exception as e:
        print(f"خطأ في حساب إحصائيات الملف: {e}")
        return {}

def colorize_output(text: str, color: str = "white") -> str:
    """تلوين النص للعرض في Terminal"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    
    color_code = colors.get(color.lower(), colors["white"])
    reset_code = colors["reset"]
    
    return f"{color_code}{text}{reset_code}"

def progress_bar(current: int, total: int, width: int = 50) -> str:
    """إنشاء شريط تقدم"""
    if total == 0:
        return "[" + "=" * width + "]"
    
    progress = current / total
    filled = int(width * progress)
    bar = "=" * filled + "-" * (width - filled)
    percentage = int(progress * 100)
    
    return f"[{bar}] {percentage}% ({current}/{total})"

def estimate_complexity(code: str) -> str:
    """تقدير تعقيد الكود"""
    lines = [line.strip() for line in code.split('\n') if line.strip()]
    code_lines = [line for line in lines if not line.startswith('#')]
    
    # عدد الأسطر
    line_count = len(code_lines)
    
    # عدد الدوال
    function_count = len(extract_functions_from_code(code))
    
    # عدد الحلقات والشروط
    control_structures = 0
    for line in code_lines:
        if any(keyword in line for keyword in ['for ', 'while ', 'if ', 'elif ', 'try:', 'except']):
            control_structures += 1
    
    # تقدير التعقيد
    complexity_score = line_count + function_count * 2 + control_structures * 3
    
    if complexity_score < 20:
        return "بسيط"
    elif complexity_score < 50:
        return "متوسط"
    else:
        return "معقد"

def generate_code_summary(code: str) -> Dict[str, Any]:
    """توليد ملخص شامل للكود"""
    functions = extract_functions_from_code(code)
    imports = extract_imports_from_code(code)
    stats = {"lines_total": len(code.split('\n'))}
    complexity = estimate_complexity(code)
    is_valid, syntax_msg = validate_python_syntax(code)
    
    return {
        "functions": functions,
        "function_count": len(functions),
        "imports": imports,
        "import_count": len(imports),
        "lines": stats.get("lines_total", 0),
        "complexity": complexity,
        "syntax_valid": is_valid,
        "syntax_message": syntax_msg,
        "estimated_execution_time": "غير محدد",
        "recommendations": _generate_code_recommendations(code, functions, imports)
    }

def _generate_code_recommendations(code: str, functions: List[str], imports: List[str]) -> List[str]:
    """توليد توصيات لتحسين الكود"""
    recommendations = []
    
    # فحص وجود دالة main
    if "main" not in functions and "if __name__" not in code:
        recommendations.append("إضافة دالة main() لتنظيم الكود")
    
    # فحص معالجة الأخطاء
    if "try" not in code and "except" not in code:
        recommendations.append("إضافة معالجة للأخطاء (try-except)")
    
    # فحص التوثيق
    if '"""' not in code and "'''" not in code:
        recommendations.append("إضافة توثيق للدوال والكود")
    
    # فحص طول الدوال
    lines = code.split('\n')
    current_function_lines = 0
    for line in lines:
        if line.strip().startswith('def '):
            current_function_lines = 0
        elif line.strip():
            current_function_lines += 1
            if current_function_lines > 20:
                recommendations.append("تقسيم الدوال الطويلة إلى دوال أصغر")
                break
    
    return recommendations

class Timer:
    """مؤقت لقياس الأداء"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """بدء التوقيت"""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """إيقاف التوقيت"""
        self.end_time = time.time()
        return self
    
    def elapsed(self) -> float:
        """الحصول على الوقت المنقضي"""
        if self.start_time is None:
            return 0.0
        
        end = self.end_time or time.time()
        return end - self.start_time
    
    def __enter__(self):
        return self.start()
    
    def __exit__(self, *args):
        self.stop()

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار الأدوات
    sample_code = '''
def hello_world():
    """دالة بسيطة لطباعة مرحبا"""
    print("مرحبا بالعالم!")

def calculate_sum(a, b):
    return a + b

if __name__ == "__main__":
    hello_world()
    result = calculate_sum(5, 3)
    print(f"النتيجة: {result}")
'''
    
    print("🔍 تحليل الكود:")
    summary = generate_code_summary(sample_code)
    
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print(f"\n⏱️ تقدير التعقيد: {estimate_complexity(sample_code)}")
    
    # اختبار المؤقت
    with Timer() as timer:
        time.sleep(0.1)  # محاكاة عملية
    
    print(f"⏱️ الوقت المنقضي: {timer.elapsed():.3f} ثانية")
