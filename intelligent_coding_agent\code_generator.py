"""
مولد الكود الذكي - ينتج كود Python عالي الجودة
"""
import re
import ast
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from task_analyzer import TaskAnalysis, TaskStep
from config import Config

@dataclass
class GeneratedCode:
    """كود مولد مع معلومات إضافية"""
    code: str
    filename: str
    description: str
    imports: List[str]
    functions: List[str]
    classes: List[str]
    test_cases: List[str]
    is_executable: bool = True

class CodeGenerator:
    """مولد الكود الذكي"""
    
    def __init__(self):
        self.code_templates = {
            "algorithm": self._generate_algorithm_code,
            "math_calculation": self._generate_math_code,
            "file_processing": self._generate_file_processing_code,
            "data_structure": self._generate_data_structure_code,
            "web_scraping": self._generate_web_scraping_code,
            "gui": self._generate_gui_code,
            "automation": self._generate_automation_code,
            "general": self._generate_general_code,
        }
    
    def generate_code(self, task_description: str, analysis: TaskAnalysis) -> GeneratedCode:
        """توليد الكود حسب تحليل المهمة"""
        
        # اختيار المولد المناسب
        generator = self.code_templates.get(analysis.task_type, self._generate_general_code)
        
        # توليد الكود
        code_info = generator(task_description, analysis)
        
        # تحليل الكود المولد
        imports, functions, classes = self._analyze_generated_code(code_info["code"])
        
        # توليد حالات الاختبار
        test_cases = self._generate_test_cases(task_description, code_info["code"], analysis.task_type)
        
        return GeneratedCode(
            code=code_info["code"],
            filename=code_info["filename"],
            description=code_info["description"],
            imports=imports,
            functions=functions,
            classes=classes,
            test_cases=test_cases,
            is_executable=self._validate_syntax(code_info["code"])
        )
    
    def _generate_algorithm_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود الخوارزميات"""
        
        # تحديد نوع الخوارزمية
        if "أعداد أولية" in task_description or "prime" in task_description.lower():
            code = '''def is_prime(n):
    """فحص ما إذا كان العدد أولي"""
    if n < 2:
        return False
    for i in range(2, int(n ** 0.5) + 1):
        if n % i == 0:
            return False
    return True

def get_primes(limit):
    """الحصول على جميع الأعداد الأولية حتى الحد المحدد"""
    primes = []
    for num in range(2, limit + 1):
        if is_prime(num):
            primes.append(num)
    return primes

# تشغيل البرنامج
if __name__ == "__main__":
    limit = 100
    prime_numbers = get_primes(limit)
    print(f"الأعداد الأولية حتى {limit}:")
    print(prime_numbers)
    print(f"عدد الأعداد الأولية: {len(prime_numbers)}")'''
            
            return {
                "code": code,
                "filename": "prime_numbers.py",
                "description": "برنامج لحساب الأعداد الأولية"
            }
        
        elif "فيبوناتشي" in task_description or "fibonacci" in task_description.lower():
            code = '''def fibonacci_sequence(n):
    """توليد متتالية فيبوناتشي حتى العدد n"""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    sequence = [0, 1]
    for i in range(2, n):
        next_num = sequence[i-1] + sequence[i-2]
        sequence.append(next_num)
    
    return sequence

def fibonacci_nth(n):
    """الحصول على العنصر رقم n في متتالية فيبوناتشي"""
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    
    return b

# تشغيل البرنامج
if __name__ == "__main__":
    n = 10
    sequence = fibonacci_sequence(n)
    print(f"أول {n} أرقام في متتالية فيبوناتشي:")
    print(sequence)
    
    nth_number = fibonacci_nth(n)
    print(f"العنصر رقم {n} في المتتالية: {nth_number}")'''
            
            return {
                "code": code,
                "filename": "fibonacci.py",
                "description": "برنامج لحساب متتالية فيبوناتشي"
            }
        
        # خوارزمية عامة
        return self._generate_general_algorithm(task_description)
    
    def _generate_math_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود العمليات الرياضية"""
        
        if "مجموع" in task_description or "sum" in task_description.lower():
            code = '''def calculate_sum(numbers):
    """حساب مجموع قائمة من الأرقام"""
    return sum(numbers)

def sum_range(start, end):
    """حساب مجموع الأرقام في نطاق معين"""
    return sum(range(start, end + 1))

def sum_formula(n):
    """حساب مجموع الأرقام من 1 إلى n باستخدام الصيغة"""
    return n * (n + 1) // 2

# تشغيل البرنامج
if __name__ == "__main__":
    # مثال 1: مجموع قائمة
    numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    result1 = calculate_sum(numbers)
    print(f"مجموع القائمة {numbers}: {result1}")
    
    # مثال 2: مجموع نطاق
    start, end = 1, 10
    result2 = sum_range(start, end)
    print(f"مجموع الأرقام من {start} إلى {end}: {result2}")
    
    # مثال 3: استخدام الصيغة
    n = 10
    result3 = sum_formula(n)
    print(f"مجموع الأرقام من 1 إلى {n} (بالصيغة): {result3}")'''
            
            return {
                "code": code,
                "filename": "math_calculations.py",
                "description": "برنامج للعمليات الرياضية"
            }
        
        return self._generate_general_math(task_description)
    
    def _generate_file_processing_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود معالجة الملفات"""
        
        code = '''import os
import json
import csv
from typing import List, Dict, Any

def read_text_file(filename: str) -> str:
    """قراءة ملف نصي"""
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"الملف {filename} غير موجود")
        return ""
    except Exception as e:
        print(f"خطأ في قراءة الملف: {e}")
        return ""

def write_text_file(filename: str, content: str) -> bool:
    """كتابة ملف نصي"""
    try:
        with open(filename, 'w', encoding='utf-8') as file:
            file.write(content)
        return True
    except Exception as e:
        print(f"خطأ في كتابة الملف: {e}")
        return False

def read_json_file(filename: str) -> Dict[str, Any]:
    """قراءة ملف JSON"""
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        print(f"خطأ في قراءة ملف JSON: {e}")
        return {}

def write_json_file(filename: str, data: Dict[str, Any]) -> bool:
    """كتابة ملف JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"خطأ في كتابة ملف JSON: {e}")
        return False

# تشغيل البرنامج
if __name__ == "__main__":
    # مثال على الاستخدام
    test_data = {"name": "أحمد", "age": 25, "city": "القاهرة"}
    
    # كتابة JSON
    if write_json_file("test_data.json", test_data):
        print("تم حفظ البيانات بنجاح")
    
    # قراءة JSON
    loaded_data = read_json_file("test_data.json")
    print(f"البيانات المحملة: {loaded_data}")'''
        
        return {
            "code": code,
            "filename": "file_processor.py",
            "description": "برنامج لمعالجة الملفات"
        }
    
    def _generate_general_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود عام"""
        
        code = f'''"""
{task_description}
"""

def main():
    """الدالة الرئيسية للبرنامج"""
    print("بدء تنفيذ المهمة: {task_description}")
    
    # TODO: إضافة منطق البرنامج هنا
    result = "تم تنفيذ المهمة بنجاح"
    
    print(f"النتيجة: {{result}}")
    return result

if __name__ == "__main__":
    main()'''
        
        return {
            "code": code,
            "filename": "main.py",
            "description": f"برنامج لتنفيذ: {task_description}"
        }
    
    def _analyze_generated_code(self, code: str) -> Tuple[List[str], List[str], List[str]]:
        """تحليل الكود المولد لاستخراج المعلومات"""
        imports = []
        functions = []
        classes = []
        
        try:
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
                elif isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    classes.append(node.name)
        
        except SyntaxError:
            pass  # في حالة وجود خطأ في الصيغة
        
        return imports, functions, classes
    
    def _generate_test_cases(self, task_description: str, code: str, task_type: str) -> List[str]:
        """توليد حالات اختبار للكود"""
        test_cases = []
        
        # استخراج أسماء الدوال من الكود
        functions = re.findall(r'def (\w+)\(', code)
        
        for func_name in functions:
            if func_name != "main" and not func_name.startswith("_"):
                test_cases.append(f"اختبار دالة {func_name}")
        
        # إضافة اختبارات عامة
        test_cases.extend([
            "اختبار تشغيل البرنامج بدون أخطاء",
            "اختبار صحة النتائج",
            "اختبار التعامل مع الحالات الاستثنائية"
        ])
        
        return test_cases
    
    def _validate_syntax(self, code: str) -> bool:
        """التحقق من صحة صيغة الكود"""
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False
    
    # دوال مساعدة إضافية
    def _generate_general_algorithm(self, task_description: str) -> Dict[str, str]:
        """توليد خوارزمية عامة"""
        return {
            "code": f'# خوارزمية لحل: {task_description}\n\ndef solve_problem():\n    """حل المشكلة المطلوبة"""\n    pass\n\nif __name__ == "__main__":\n    solve_problem()',
            "filename": "algorithm.py",
            "description": f"خوارزمية لحل: {task_description}"
        }
    
    def _generate_general_math(self, task_description: str) -> Dict[str, str]:
        """توليد كود رياضي عام"""
        return {
            "code": f'import math\n\ndef calculate():\n    """حساب رياضي لـ: {task_description}"""\n    pass\n\nif __name__ == "__main__":\n    result = calculate()\n    print(f"النتيجة: {{result}}")',
            "filename": "math_solver.py",
            "description": f"حاسبة لـ: {task_description}"
        }

    def _generate_data_structure_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود هياكل البيانات"""

        code = '''class Stack:
    """مكدس (Stack) - هيكل بيانات LIFO"""

    def __init__(self):
        self.items = []

    def push(self, item):
        """إضافة عنصر للمكدس"""
        self.items.append(item)

    def pop(self):
        """إزالة وإرجاع آخر عنصر"""
        if not self.is_empty():
            return self.items.pop()
        return None

    def is_empty(self):
        """فحص ما إذا كان المكدس فارغ"""
        return len(self.items) == 0

# تشغيل البرنامج
if __name__ == "__main__":
    stack = Stack()
    stack.push(1)
    stack.push(2)
    print(f"Pop: {stack.pop()}")'''

        return {
            "code": code,
            "filename": "data_structures.py",
            "description": "هياكل البيانات الأساسية"
        }

    def _generate_web_scraping_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود استخراج البيانات من الويب"""

        code = '''# ملاحظة: يتطلب تثبيت: pip install requests beautifulsoup4
# import requests
# from bs4 import BeautifulSoup

def simple_web_request():
    """طلب ويب بسيط"""
    try:
        # محاكاة طلب ويب
        print("محاكاة استخراج البيانات من الويب...")
        data = {
            "title": "عنوان تجريبي",
            "content": "محتوى تجريبي"
        }
        return data
    except Exception as e:
        print(f"خطأ: {e}")
        return None

if __name__ == "__main__":
    result = simple_web_request()
    if result:
        print(f"العنوان: {result['title']}")
        print(f"المحتوى: {result['content']}")'''

        return {
            "code": code,
            "filename": "web_scraper.py",
            "description": "برنامج استخراج البيانات من الويب"
        }

    def _generate_gui_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود واجهة المستخدم"""

        code = '''# ملاحظة: يتطلب tkinter (مثبت افتراضياً مع Python)
import tkinter as tk
from tkinter import messagebox

class SimpleApp:
    """تطبيق بسيط مع واجهة رسومية"""

    def __init__(self, root):
        self.root = root
        self.root.title("تطبيق بسيط")
        self.root.geometry("300x200")

        # إنشاء العناصر
        self.label = tk.Label(root, text="مرحباً!", font=("Arial", 14))
        self.label.pack(pady=20)

        self.button = tk.Button(root, text="اضغط هنا", command=self.button_click)
        self.button.pack(pady=10)

    def button_click(self):
        """معالجة النقر على الزر"""
        messagebox.showinfo("رسالة", "تم النقر على الزر!")

if __name__ == "__main__":
    root = tk.Tk()
    app = SimpleApp(root)
    root.mainloop()'''

        return {
            "code": code,
            "filename": "gui_app.py",
            "description": "تطبيق مع واجهة رسومية"
        }

    def _generate_automation_code(self, task_description: str, analysis: TaskAnalysis) -> Dict[str, str]:
        """توليد كود الأتمتة"""

        code = '''import os
import time
from datetime import datetime

def automated_task():
    """مهمة تلقائية بسيطة"""
    print("بدء المهمة التلقائية...")

    # محاكاة عمل تلقائي
    for i in range(5):
        print(f"خطوة {i+1}/5")
        time.sleep(1)  # انتظار ثانية واحدة

    print("تم إنجاز المهمة التلقائية!")
    return True

def schedule_task():
    """جدولة المهمة"""
    print(f"تم تشغيل المهمة في: {datetime.now()}")
    return automated_task()

if __name__ == "__main__":
    schedule_task()'''

        return {
            "code": code,
            "filename": "automation.py",
            "description": "برنامج أتمتة المهام"
        }
