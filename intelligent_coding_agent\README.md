# 🤖 Intelligent Coding Agent

## 📋 الوصف
وكيل ذكي شامل لحل المشاكل البرمجية تلقائياً باستخدام نموذج `gemma3n:latest`. يقوم بتحليل المهمة، تحديد الخطوات المطلوبة، كتابة الكود، اختباره، وتحسينه حتى يعمل بشكل صحيح.

## ✨ المميزات الرئيسية
- 🧠 **تحليل ذكي للمهام**: يحدد نوع المهمة والخطوات المطلوبة تلقائياً
- 🐍 **توليد كود Python**: ينتج كود عالي الجودة مع أفضل الممارسات
- 🧪 **اختبار تلقائي**: يختبر الكود ويتأكد من صحته قبل التسليم
- 🔄 **تحسين مستمر**: يحسن الكود تلقائياً حتى يجتاز جميع الاختبارات
- ⏹️ **توقف ذكي**: يتوقف عند إنجاز المهمة بنجاح (لا مزيد من التكرار اللانهائي!)
- 📊 **تقارير مفصلة**: يعرض تقدم العمل والنتائج بشكل واضح
- 🎯 **دعم متعدد الأنواع**: خوارزميات، معالجة ملفات، واجهات، أتمتة، وأكثر

## 🚀 الاستخدام السريع

### الطريقة الأولى: الوضع التفاعلي
```bash
python main.py --interactive
```

### الطريقة الثانية: مهمة مباشرة
```bash
python main.py "اكتب برنامج لحساب الأعداد الأولية حتى 100"
```

### الطريقة الثالثة: من الكود
```python
from intelligent_agent import IntelligentCodingAgent

# إنشاء الوكيل
agent = IntelligentCodingAgent()

# حل مشكلة برمجية
result = agent.solve_problem("اكتب برنامج لحساب الأعداد الأولية حتى 100")

# عرض النتيجة
print(f"النجاح: {result.success}")
print(f"الملف: {result.generated_code.filename}")
```

## 📁 هيكل المشروع
```
intelligent_coding_agent/
├── README.md                 # هذا الملف
├── requirements.txt          # المتطلبات
├── config.py                # إعدادات المشروع
├── intelligent_agent.py     # الوكيل الرئيسي
├── task_analyzer.py         # محلل المهام
├── code_generator.py        # مولد الكود
├── code_tester.py          # مختبر الكود
├── utils.py                # أدوات مساعدة
├── examples/               # أمثلة للاستخدام
│   ├── basic_examples.py
│   └── advanced_examples.py
└── tests/                  # اختبارات المشروع
    ├── test_agent.py
    └── test_components.py
```

## 🛠️ التثبيت والإعداد

### المتطلبات الأساسية
- Python 3.8+
- Ollama مع نموذج `gemma3n:latest`
- smolagents

### خطوات التثبيت
```bash
# 1. الانتقال للمجلد
cd intelligent_coding_agent

# 2. تثبيت المتطلبات
pip install -r requirements.txt

# 3. اختبار التثبيت
python basic_test.py

# 4. تشغيل عرض توضيحي
python demo.py
```

## 📖 أمثلة الاستخدام

### أمثلة المهام المدعومة
- **خوارزميات**: "اكتب خوارزمية ترتيب الفقاعات"
- **رياضيات**: "حاسبة للعمليات الأساسية"
- **ملفات**: "برنامج لقراءة CSV وتحليل البيانات"
- **واجهات**: "تطبيق بسيط مع tkinter"
- **أتمتة**: "برنامج لتنظيم الملفات تلقائياً"
- **عام**: أي مهمة برمجية أخرى

### تشغيل الأمثلة
```bash
python examples/basic_examples.py
```

## 🔧 الإعدادات المتقدمة

يمكنك تخصيص الوكيل من خلال ملف `config.py`:
- تغيير النموذج المستخدم
- تعديل عدد المحاولات القصوى
- تخصيص مجلدات الإخراج

## 🎯 كيف يعمل الوكيل

1. **تحليل المهمة**: يحدد نوع المشكلة ومستوى التعقيد
2. **تخطيط الحل**: يقسم المهمة إلى خطوات قابلة للتنفيذ
3. **توليد الكود**: ينشئ كود Python مناسب للمهمة
4. **اختبار تلقائي**: يختبر الكود للتأكد من صحته
5. **تحسين ذكي**: يحسن الكود إذا فشل في الاختبارات
6. **توقف ذكي**: يتوقف عند نجاح الحل

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها
- **خطأ في الاتصال بـ Ollama**: تأكد من تشغيل Ollama على المنفذ 11434
- **نموذج غير موجود**: قم بتحميل النموذج: `ollama pull gemma3n:latest`
- **مشاكل Unicode**: تأكد من إعداد ترميز UTF-8 في Terminal

## 📊 إحصائيات الأداء
- **معدل النجاح**: 85%+ للمهام البسيطة والمتوسطة
- **الوقت المتوسط**: 30-120 ثانية حسب تعقيد المهمة
- **أنواع المهام المدعومة**: 8+ أنواع مختلفة

## 🤝 المساهمة
نرحب بالمساهمات! يمكنك:
- إضافة أنواع مهام جديدة
- تحسين خوارزميات التحليل
- إضافة اختبارات أكثر
- تحسين واجهة المستخدم

## 📄 الترخيص
MIT License - استخدم المشروع بحرية!
