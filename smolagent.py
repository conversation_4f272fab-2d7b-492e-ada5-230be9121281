from smolagents import CodeAgent
from smolagents.models import OpenAIModel
from openai import OpenAI

# إعداد عميل openai على ollama
client = OpenAI(
    base_url="http://localhost:11434/v1",
    api_key="ollama"  # أي قيمة غير فاضية
)

# إعداد نموذج OpenAIModel لتمريره إلى smolagents
model = OpenAIModel(model="gemma3:1b", client=client)

# إنشاء الوكيل
agent = CodeAgent(tools=[], model=model)

# تنفيذ مهمة بسيطة
result = agent.run("احسب مجموع الأعداد من 1 إلى 10")
print(result)
