from smolagents import CodeAgent
from smolagents.models import OpenAIModel
from openai import OpenAI
import re
import time

# إعداد عميل openai على ollama
client = OpenAI(
    base_url="http://localhost:11434/v1",
    api_key="ollama"  # أي قيمة غير فاضية
)

class SmartAgent:
    """وكيل ذكي مع آليات توقف متقدمة"""

    def __init__(self, model):
        self.agent = CodeAgent(tools=[], model=model)
        self.completion_patterns = [
            r'execution logs:\s*(\d+)',  # رقم في execution logs
            r'print.*?(\d+)',            # رقم مطبوع
            r'النتيجة.*?(\d+)',          # النتيجة مع رقم
            r'المجموع.*?(\d+)',          # المجموع مع رقم
            r'الناتج.*?(\d+)',           # الناتج مع رقم
        ]

    def run_smart(self, task, max_steps=3, target_answer=None):
        """تشغيل ذكي مع توقف تلقائي"""
        print(f"🚀 بدء المهمة الذكية: {task}")
        result = None  # تعريف المتغير في البداية

        # تحسين الـ prompt للوضوح الأقصى
        enhanced_prompt = f"""
{task}

تعليمات دقيقة:
1. نفذ الحساب بدقة
2. اطبع النتيجة الرقمية النهائية فقط
3. توقف فوراً بعد طباعة الرقم الصحيح
4. لا تحتاج لتفسير أو تحليل إضافي

مثال: إذا كان الجواب 55، اطبع: 55
"""

        for step in range(1, max_steps + 1):
            print(f"\n📍 الخطوة {step}:")
            start_time = time.time()

            try:
                # تشغيل خطوة واحدة
                result = self.agent.run(enhanced_prompt, max_steps=1)
                duration = time.time() - start_time

                print(f"⏱️ مدة الخطوة: {duration:.1f} ثانية")

                # تحليل النتيجة للتحقق من الاكتمال
                extracted_number = self._extract_answer(result)

                if extracted_number:
                    print(f"🎯 تم استخراج الرقم: {extracted_number}")

                    # إذا كان لدينا إجابة متوقعة، نتحقق منها
                    if target_answer and str(extracted_number) == str(target_answer):
                        print("✅ الإجابة صحيحة! توقف تلقائي.")
                        return result
                    elif not target_answer:
                        print("✅ تم العثور على إجابة رقمية! توقف تلقائي.")
                        return result

                print(f"⏳ الخطوة {step} مكتملة، البحث عن إجابة أوضح...")

            except Exception as e:
                print(f"❌ خطأ في الخطوة {step}: {e}")
                break

        print(f"⚠️ انتهت الخطوات المسموحة ({max_steps})")
        try:
            return result
        except NameError:
            return "لم يتم الحصول على نتيجة"

    def _extract_answer(self, result):
        """استخراج الإجابة الرقمية من النتيجة"""
        result_str = str(result)

        # البحث في الأنماط المختلفة
        for pattern in self.completion_patterns:
            matches = re.findall(pattern, result_str)
            if matches:
                # إرجاع آخر رقم موجود (عادة هو الإجابة النهائية)
                return matches[-1]

        # البحث عن أي رقم في النص
        numbers = re.findall(r'\b\d+\b', result_str)
        if numbers:
            # تصفية الأرقام الصغيرة (مثل أرقام الأسطر)
            significant_numbers = [n for n in numbers if int(n) > 10]
            if significant_numbers:
                return significant_numbers[-1]
            return numbers[-1]

        return None

# إعداد نموذج OpenAIModel لتمريره إلى smolagents
model = OpenAIModel(model_id="gemma3n:latest", api_base="http://localhost:11434/v1", api_key="ollama")

# إنشاء الوكيل الذكي
smart_agent = SmartAgent(model)

# تجربة بسيطة مع التوقف الذكي
print("="*50)
print("🧠 الوكيل الذكي مع gemma3n:latest")
print("="*50)

# مهمة واحدة بسيطة
result = smart_agent.run_smart(
    "احسب مجموع الأعداد من 1 إلى 10",
    max_steps=2,
    target_answer=55
)

print("\n" + "="*50)
print("🏆 النتيجة النهائية:")
print(result)
print("="*50)
