"""
أمثلة أساسية لاستخدام الوكيل الذكي
"""
import sys
import os

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from intelligent_agent import IntelligentCodingAgent

def example_1_prime_numbers():
    """مثال 1: حساب الأعداد الأولية"""
    print("🔢 مثال 1: حساب الأعداد الأولية")
    print("-" * 50)
    
    agent = IntelligentCodingAgent()
    
    task = "اكتب برنامج لحساب جميع الأعداد الأولية من 1 إلى 100"
    result = agent.solve_problem(task)
    
    print(f"\n✅ النتيجة: {'نجح' if result.success else 'فشل'}")
    return result

def example_2_fibonacci():
    """مثال 2: متتالية فيبوناتشي"""
    print("\n🌀 مثال 2: متتالية فيبوناتشي")
    print("-" * 50)
    
    agent = IntelligentCodingAgent()
    
    task = "اكتب برنامج لحساب أول 20 رقم في متتالية فيبوناتشي"
    result = agent.solve_problem(task)
    
    print(f"\n✅ النتيجة: {'نجح' if result.success else 'فشل'}")
    return result

def example_3_file_processing():
    """مثال 3: معالجة الملفات"""
    print("\n📁 مثال 3: معالجة الملفات")
    print("-" * 50)
    
    agent = IntelligentCodingAgent()
    
    task = "اكتب برنامج لقراءة ملف نصي وحساب عدد الكلمات والأسطر فيه"
    result = agent.solve_problem(task)
    
    print(f"\n✅ النتيجة: {'نجح' if result.success else 'فشل'}")
    return result

def example_4_math_calculator():
    """مثال 4: حاسبة رياضية"""
    print("\n🧮 مثال 4: حاسبة رياضية")
    print("-" * 50)
    
    agent = IntelligentCodingAgent()
    
    task = "اكتب حاسبة بسيطة تقوم بالعمليات الأساسية (جمع، طرح، ضرب، قسمة)"
    result = agent.solve_problem(task)
    
    print(f"\n✅ النتيجة: {'نجح' if result.success else 'فشل'}")
    return result

def example_5_data_analysis():
    """مثال 5: تحليل البيانات البسيط"""
    print("\n📊 مثال 5: تحليل البيانات")
    print("-" * 50)
    
    agent = IntelligentCodingAgent()
    
    task = "اكتب برنامج لتحليل قائمة من الدرجات وحساب المتوسط والحد الأقصى والأدنى"
    result = agent.solve_problem(task)
    
    print(f"\n✅ النتيجة: {'نجح' if result.success else 'فشل'}")
    return result

def run_all_examples():
    """تشغيل جميع الأمثلة"""
    print("🚀 تشغيل جميع الأمثلة الأساسية")
    print("=" * 80)
    
    examples = [
        example_1_prime_numbers,
        example_2_fibonacci,
        example_3_file_processing,
        example_4_math_calculator,
        example_5_data_analysis,
    ]
    
    results = []
    successful = 0
    
    for example_func in examples:
        try:
            result = example_func()
            results.append(result)
            if result.success:
                successful += 1
        except Exception as e:
            print(f"❌ خطأ في تشغيل المثال: {e}")
            results.append(None)
    
    # ملخص النتائج
    print("\n" + "=" * 80)
    print("📊 ملخص النتائج")
    print("=" * 80)
    print(f"✅ الأمثلة الناجحة: {successful}/{len(examples)}")
    print(f"📁 الملفات المحفوظة في مجلد: output/")
    
    for i, result in enumerate(results, 1):
        if result:
            status = "✅ نجح" if result.success else "❌ فشل"
            print(f"  {i}. {result.task_description[:50]}... - {status}")
        else:
            print(f"  {i}. خطأ في التنفيذ - ❌ فشل")
    
    print("=" * 80)
    
    return results

if __name__ == "__main__":
    # يمكنك تشغيل مثال واحد أو جميع الأمثلة
    
    # تشغيل مثال واحد
    # example_1_prime_numbers()
    
    # تشغيل جميع الأمثلة
    run_all_examples()
