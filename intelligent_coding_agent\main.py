#!/usr/bin/env python3
"""
الملف الرئيسي لتشغيل الوكيل الذكي
"""
import sys
import os
import argparse
from typing import Optional

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_agent import IntelligentCodingAgent
from config import Config
from utils import colorize_output, Timer

def main():
    """الدالة الرئيسية"""
    
    # إعداد معالج الأوامر
    parser = argparse.ArgumentParser(
        description="🤖 الوكيل الذكي لحل المشاكل البرمجية",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python main.py "اكتب برنامج لحساب الأعداد الأولية حتى 100"
  python main.py --interactive
  python main.py --examples
  python main.py --task "حاسبة بسيطة" --model "gemma3n:latest"
        """
    )
    
    parser.add_argument(
        "task",
        nargs="?",
        help="وصف المهمة البرمجية المطلوب حلها"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="تشغيل الوضع التفاعلي"
    )
    
    parser.add_argument(
        "--examples", "-e",
        action="store_true",
        help="تشغيل الأمثلة التوضيحية"
    )
    
    parser.add_argument(
        "--model", "-m",
        default=Config.MODEL_ID,
        help=f"النموذج المستخدم (افتراضي: {Config.MODEL_ID})"
    )
    
    parser.add_argument(
        "--max-iterations",
        type=int,
        default=Config.MAX_RETRIES,
        help=f"الحد الأقصى للتكرارات (افتراضي: {Config.MAX_RETRIES})"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="عرض تفاصيل أكثر أثناء التنفيذ"
    )
    
    parser.add_argument(
        "--output-dir",
        default=Config.OUTPUT_DIR,
        help=f"مجلد حفظ النتائج (افتراضي: {Config.OUTPUT_DIR})"
    )
    
    args = parser.parse_args()
    
    # طباعة الترحيب
    print_welcome()
    
    # تحديد وضع التشغيل
    if args.examples:
        run_examples()
    elif args.interactive:
        run_interactive_mode(args)
    elif args.task:
        run_single_task(args.task, args)
    else:
        print("❌ يجب تحديد مهمة أو استخدام الوضع التفاعلي")
        parser.print_help()
        sys.exit(1)

def print_welcome():
    """طباعة رسالة الترحيب"""
    welcome_text = """
🤖 الوكيل الذكي لحل المشاكل البرمجية
=====================================

مرحباً! أنا وكيل ذكي متخصص في حل المشاكل البرمجية.
يمكنني:
• تحليل المهام البرمجية
• كتابة كود Python عالي الجودة  
• اختبار الكود تلقائياً
• تحسين الحلول حتى تعمل بشكل مثالي
• التوقف الذكي عند إنجاز المهمة

"""
    print(colorize_output(welcome_text, "cyan"))

def run_single_task(task: str, args) -> None:
    """تشغيل مهمة واحدة"""
    
    print(f"🎯 المهمة: {task}")
    print("-" * 60)
    
    # إنشاء الوكيل
    agent = IntelligentCodingAgent(
        model_id=args.model,
        api_base=Config.API_BASE,
        api_key=Config.API_KEY
    )
    
    # تشغيل المهمة مع قياس الوقت
    with Timer() as timer:
        try:
            result = agent.solve_problem(
                task_description=task,
                max_iterations=args.max_iterations
            )
            
            # عرض النتيجة
            if result.success:
                print(colorize_output(f"\n✅ تم حل المهمة بنجاح!", "green"))
                print(f"📁 الملف محفوظ في: {Config.get_output_path(result.generated_code.filename)}")
            else:
                print(colorize_output(f"\n⚠️ المهمة تحتاج تحسينات إضافية", "yellow"))
                print("💡 راجع التوصيات في التقرير أعلاه")
            
        except KeyboardInterrupt:
            print(colorize_output(f"\n⏹️ تم إيقاف العملية بواسطة المستخدم", "yellow"))
        except Exception as e:
            print(colorize_output(f"\n❌ خطأ في تنفيذ المهمة: {e}", "red"))
    
    print(f"\n⏱️ إجمالي الوقت: {timer.elapsed():.2f} ثانية")

def run_interactive_mode(args) -> None:
    """تشغيل الوضع التفاعلي"""
    
    print(colorize_output("🔄 الوضع التفاعلي - اكتب 'exit' للخروج", "blue"))
    print("-" * 60)
    
    # إنشاء الوكيل
    agent = IntelligentCodingAgent(
        model_id=args.model,
        api_base=Config.API_BASE,
        api_key=Config.API_KEY
    )
    
    task_counter = 1
    
    while True:
        try:
            # طلب المهمة من المستخدم
            task = input(f"\n📝 المهمة #{task_counter}: ").strip()
            
            if not task:
                continue
            
            if task.lower() in ['exit', 'quit', 'خروج']:
                print(colorize_output("👋 وداعاً!", "green"))
                break
            
            if task.lower() in ['help', 'مساعدة']:
                print_help()
                continue
            
            if task.lower() in ['capabilities', 'قدرات']:
                print_capabilities(agent)
                continue
            
            # تنفيذ المهمة
            print(f"\n🚀 بدء حل المهمة #{task_counter}...")
            
            with Timer() as timer:
                result = agent.solve_problem(
                    task_description=task,
                    max_iterations=args.max_iterations
                )
            
            # عرض النتيجة المختصرة
            status = "✅ نجح" if result.success else "⚠️ يحتاج تحسين"
            print(f"\n📊 النتيجة: {status} (⏱️ {timer.elapsed():.1f}s)")
            
            if result.success:
                print(f"📁 الملف: {result.generated_code.filename}")
            
            task_counter += 1
            
        except KeyboardInterrupt:
            print(colorize_output(f"\n👋 تم الخروج من الوضع التفاعلي", "yellow"))
            break
        except Exception as e:
            print(colorize_output(f"\n❌ خطأ: {e}", "red"))

def run_examples() -> None:
    """تشغيل الأمثلة التوضيحية"""
    
    print(colorize_output("📚 تشغيل الأمثلة التوضيحية", "blue"))
    print("-" * 60)
    
    try:
        from examples.basic_examples import run_all_examples
        run_all_examples()
    except ImportError:
        print(colorize_output("❌ لم يتم العثور على ملف الأمثلة", "red"))
    except Exception as e:
        print(colorize_output(f"❌ خطأ في تشغيل الأمثلة: {e}", "red"))

def print_help() -> None:
    """طباعة المساعدة"""
    help_text = """
🆘 المساعدة:

الأوامر المتاحة:
• اكتب وصف المهمة البرمجية مباشرة
• 'help' أو 'مساعدة' - عرض هذه المساعدة
• 'capabilities' أو 'قدرات' - عرض قدرات الوكيل
• 'exit' أو 'خروج' - الخروج من البرنامج

أمثلة المهام:
• "اكتب برنامج لحساب الأعداد الأولية"
• "حاسبة بسيطة للعمليات الأساسية"
• "برنامج لقراءة ملف CSV وتحليل البيانات"
• "خوارزمية ترتيب الفقاعات"
• "برنامج لتحويل درجات الحرارة"
"""
    print(colorize_output(help_text, "yellow"))

def print_capabilities(agent: IntelligentCodingAgent) -> None:
    """طباعة قدرات الوكيل"""
    capabilities = agent.get_capabilities()
    
    print(colorize_output("🔧 قدرات الوكيل:", "blue"))
    print(f"🤖 النموذج: {capabilities['model']}")
    print(f"🔄 الحد الأقصى للتكرارات: {capabilities['max_iterations']}")
    
    print("\n📋 أنواع المهام المدعومة:")
    for task_type in capabilities['supported_tasks']:
        print(f"  • {task_type}")
    
    print("\n✨ المميزات:")
    for feature in capabilities['features']:
        print(f"  • {feature}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(colorize_output(f"\n👋 تم إنهاء البرنامج", "yellow"))
        sys.exit(0)
    except Exception as e:
        print(colorize_output(f"\n❌ خطأ عام: {e}", "red"))
        sys.exit(1)
