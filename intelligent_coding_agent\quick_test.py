#!/usr/bin/env python3
"""
اختبار سريع للوكيل الذكي
"""
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_agent import IntelligentCodingAgent
from utils import Timer, colorize_output

def quick_test():
    """اختبار سريع للوكيل"""
    
    print(colorize_output("🧪 اختبار سريع للوكيل الذكي", "cyan"))
    print("=" * 60)
    
    # إنشاء الوكيل
    try:
        print("🤖 إنشاء الوكيل...")
        agent = IntelligentCodingAgent()
        print("✅ تم إنشاء الوكيل بنجاح")
    except Exception as e:
        print(colorize_output(f"❌ فشل في إنشاء الوكيل: {e}", "red"))
        return False
    
    # اختبار بسيط
    test_task = "اكتب برنامج بسيط لحساب مجموع رقمين"
    
    print(f"\n📝 المهمة الاختبارية: {test_task}")
    print("-" * 60)
    
    try:
        with Timer() as timer:
            result = agent.solve_problem(test_task, max_iterations=2)
        
        print(f"\n📊 نتيجة الاختبار:")
        print(f"  ✅ النجاح: {'نعم' if result.success else 'لا'}")
        print(f"  ⏱️ الوقت: {timer.elapsed():.2f} ثانية")
        print(f"  🔄 التكرارات: {result.iterations}")
        print(f"  📁 الملف: {result.generated_code.filename}")
        
        if result.test_report:
            print(f"  🧪 الاختبارات: {result.test_report.passed_tests}/{result.test_report.total_tests}")
        
        # عرض الكود المولد (أول 10 أسطر)
        if result.final_code:
            print(f"\n📄 عينة من الكود المولد:")
            lines = result.final_code.split('\n')[:10]
            for i, line in enumerate(lines, 1):
                print(f"  {i:2d}: {line}")
            if len(result.final_code.split('\n')) > 10:
                print("  ...")
        
        return result.success
        
    except Exception as e:
        print(colorize_output(f"❌ خطأ في الاختبار: {e}", "red"))
        return False

def test_components():
    """اختبار المكونات الفردية"""
    
    print(colorize_output("\n🔧 اختبار المكونات الفردية", "blue"))
    print("-" * 60)
    
    # اختبار محلل المهام
    try:
        from task_analyzer import TaskAnalyzer
        analyzer = TaskAnalyzer()
        analysis = analyzer.analyze_task("اكتب برنامج بسيط")
        print("✅ محلل المهام يعمل")
    except Exception as e:
        print(colorize_output(f"❌ محلل المهام: {e}", "red"))
    
    # اختبار مولد الكود
    try:
        from code_generator import CodeGenerator
        generator = CodeGenerator()
        print("✅ مولد الكود يعمل")
    except Exception as e:
        print(colorize_output(f"❌ مولد الكود: {e}", "red"))
    
    # اختبار مختبر الكود
    try:
        from code_tester import CodeTester
        tester = CodeTester()
        print("✅ مختبر الكود يعمل")
    except Exception as e:
        print(colorize_output(f"❌ مختبر الكود: {e}", "red"))
    
    # اختبار الأدوات المساعدة
    try:
        from utils import generate_code_summary
        summary = generate_code_summary("def test(): pass")
        print("✅ الأدوات المساعدة تعمل")
    except Exception as e:
        print(colorize_output(f"❌ الأدوات المساعدة: {e}", "red"))

def main():
    """الدالة الرئيسية للاختبار"""
    
    print(colorize_output("🚀 بدء الاختبارات الشاملة", "green"))
    print("=" * 80)
    
    # اختبار المكونات
    test_components()
    
    # اختبار سريع
    success = quick_test()
    
    # النتيجة النهائية
    print("\n" + "=" * 80)
    if success:
        print(colorize_output("🎉 جميع الاختبارات نجحت! الوكيل جاهز للاستخدام", "green"))
        print("\n💡 يمكنك الآن تشغيل:")
        print("  python main.py --interactive")
        print("  python main.py 'اكتب برنامج لحساب الأعداد الأولية'")
    else:
        print(colorize_output("⚠️ بعض الاختبارات فشلت، راجع الأخطاء أعلاه", "yellow"))
    
    print("=" * 80)

if __name__ == "__main__":
    main()
