from smolagents import CodeAgent
from smolagents.models import OpenAIModel
from openai import OpenAI
import re

class SmartStoppingAgent:
    def __init__(self, model):
        self.agent = CodeAgent(tools=[], model=model)
        
    def run_with_smart_stopping(self, task, max_steps=5):
        """
        تشغيل الوكيل مع آلية إيقاف ذكية
        """
        print(f"🚀 بدء المهمة: {task}")
        
        # تحسين الـ prompt لتوضيح متى يجب التوقف
        enhanced_prompt = f"""
        {task}
        
        تعليمات مهمة:
        1. نفذ المهمة بدقة
        2. اطبع النتيجة النهائية بوضوح
        3. توقف فوراً بعد الحصول على النتيجة الصحيحة
        4. لا تحتاج لتحليل إضافي أو خطوات زائدة
        """
        
        # تشغيل الوكيل مع مراقبة النتائج
        for step in range(1, max_steps + 1):
            print(f"\n📍 الخطوة {step}:")
            
            try:
                # تشغيل خطوة واحدة
                result = self.agent.run(enhanced_prompt, max_steps=1)
                
                # تحليل النتيجة للتحقق من اكتمال المهمة
                if self._is_task_completed(result, task):
                    print("✅ تم اكتشاف اكتمال المهمة!")
                    return result
                    
                print(f"⏳ الخطوة {step} مكتملة، لكن المهمة تحتاج متابعة...")
                
            except Exception as e:
                print(f"❌ خطأ في الخطوة {step}: {e}")
                break
                
        print(f"⚠️ تم الوصول للحد الأقصى من الخطوات ({max_steps})")
        return result
    
    def _is_task_completed(self, result, original_task):
        """
        تحليل ذكي لتحديد ما إذا كانت المهمة مكتملة
        """
        result_str = str(result).lower()
        
        # البحث عن أنماط تدل على اكتمال المهمة
        completion_patterns = [
            r'execution logs:\s*\d+',  # وجود نتيجة رقمية في logs
            r'print.*\d+',             # طباعة رقم
            r'النتيجة.*\d+',           # كلمة "النتيجة" مع رقم
            r'المجموع.*\d+',           # كلمة "المجموع" مع رقم
        ]
        
        # إذا كانت المهمة حسابية بسيطة
        if any(word in original_task.lower() for word in ['احسب', 'مجموع', 'جمع']):
            # البحث عن رقم في النتيجة
            numbers = re.findall(r'\d+', result_str)
            if numbers:
                print(f"🔍 تم العثور على أرقام في النتيجة: {numbers}")
                # إذا وجدنا الرقم 55 (مجموع 1 إلى 10)
                if '55' in numbers:
                    return True
                    
        # فحص الأنماط العامة
        for pattern in completion_patterns:
            if re.search(pattern, result_str):
                print(f"🎯 تم العثور على نمط اكتمال: {pattern}")
                return True
                
        return False

# إعداد العميل والنموذج
client = OpenAI(
    base_url="http://localhost:11434/v1",
    api_key="ollama"
)

model = OpenAIModel(model_id="gemma3:1b", api_base="http://localhost:11434/v1", api_key="ollama")

# إنشاء الوكيل الذكي
smart_agent = SmartStoppingAgent(model)

# تجربة المهمة
if __name__ == "__main__":
    result = smart_agent.run_with_smart_stopping(
        "احسب مجموع الأعداد من 1 إلى 10", 
        max_steps=3
    )
    
    print("\n" + "="*50)
    print("🏆 النتيجة النهائية:")
    print(result)
