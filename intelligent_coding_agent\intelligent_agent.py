"""
الوكيل الذكي الشامل لحل المشاكل البرمجية
"""
import time
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from smolagents import CodeAgent
from smolagents.models import OpenAIModel
from openai import OpenAI

from config import Config, EXTRA_CONFIG
from task_analyzer import TaskAnalyzer, TaskAnalysis
from code_generator import CodeGenerator, GeneratedCode
from code_tester import CodeTester, CodeTestReport

@dataclass
class SolutionResult:
    """نتيجة حل المشكلة"""
    success: bool
    task_description: str
    analysis: TaskAnalysis
    generated_code: GeneratedCode
    test_report: CodeTestReport
    final_code: str
    execution_time: float
    iterations: int
    recommendations: List[str]

class IntelligentCodingAgent:
    """الوكيل الذكي الشامل لحل المشاكل البرمجية"""
    
    def __init__(self, model_id: str = None, api_base: str = None, api_key: str = None):
        """تهيئة الوكيل الذكي"""
        
        # إعداد النموذج
        self.model_id = model_id or Config.MODEL_ID
        self.api_base = api_base or Config.API_BASE
        self.api_key = api_key or Config.API_KEY
        
        # إنشاء النموذج
        self.model = OpenAIModel(
            model_id=self.model_id,
            api_base=self.api_base,
            api_key=self.api_key
        )
        
        # إنشاء الوكيل الأساسي
        self.base_agent = CodeAgent(tools=[], model=self.model)
        
        # إنشاء المكونات المساعدة
        self.task_analyzer = TaskAnalyzer()
        self.code_generator = CodeGenerator()
        self.code_tester = CodeTester()
        
        # إعدادات التشغيل
        self.max_iterations = Config.MAX_RETRIES
        self.debug = EXTRA_CONFIG.get("debug", False)
        self.verbose = EXTRA_CONFIG.get("verbose", False)
        
        print(f"🤖 تم تهيئة الوكيل الذكي مع النموذج: {self.model_id}")
    
    def solve_problem(self, task_description: str, max_iterations: int = None) -> SolutionResult:
        """حل مشكلة برمجية بشكل شامل"""
        
        start_time = time.time()
        max_iterations = max_iterations or self.max_iterations
        
        print(f"\n🚀 بدء حل المشكلة: {task_description}")
        print("="*80)
        
        # الخطوة 1: تحليل المهمة
        print("📋 الخطوة 1: تحليل المهمة...")
        analysis = self.task_analyzer.analyze_task(task_description)
        
        if self.verbose:
            self.task_analyzer.print_analysis(analysis)
        
        # الخطوة 2: توليد الكود الأولي
        print("\n💻 الخطوة 2: توليد الكود...")
        generated_code = self.code_generator.generate_code(task_description, analysis)
        
        if self.verbose:
            print(f"📄 تم توليد الملف: {generated_code.filename}")
            print(f"📦 المكتبات المستخدمة: {generated_code.imports}")
            print(f"🔧 الدوال المولدة: {generated_code.functions}")
        
        # الخطوة 3: اختبار وتحسين الكود
        print("\n🧪 الخطوة 3: اختبار الكود...")
        
        best_code = generated_code
        best_test_report = None
        iteration = 0
        
        for iteration in range(1, max_iterations + 1):
            print(f"\n🔄 التكرار {iteration}:")
            
            # اختبار الكود الحالي
            test_report = self.code_tester.test_code(best_code)
            
            if self.verbose:
                self.code_tester.print_test_report(test_report)
            
            # إذا نجح الاختبار، توقف
            if test_report.overall_success:
                print(f"✅ نجح الكود في التكرار {iteration}!")
                best_test_report = test_report
                break
            
            # إذا فشل، حاول التحسين
            print(f"⚠️ فشل الكود في التكرار {iteration}، محاولة التحسين...")
            
            if iteration < max_iterations:
                improved_code = self._improve_code(best_code, test_report, task_description)
                if improved_code:
                    best_code = improved_code
                    print("🔧 تم تحسين الكود")
                else:
                    print("❌ لم يتم تحسين الكود")
            
            best_test_report = test_report
        
        # الخطوة 4: حفظ النتيجة النهائية
        final_code = self._finalize_code(best_code, best_test_report)
        
        # حفظ الكود في ملف
        output_file = Config.get_output_path(best_code.filename)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_code)
        
        execution_time = time.time() - start_time
        
        # إنشاء النتيجة النهائية
        result = SolutionResult(
            success=best_test_report.overall_success if best_test_report else False,
            task_description=task_description,
            analysis=analysis,
            generated_code=best_code,
            test_report=best_test_report,
            final_code=final_code,
            execution_time=execution_time,
            iterations=iteration,
            recommendations=self._generate_final_recommendations(best_test_report, analysis)
        )
        
        # طباعة النتيجة النهائية
        self._print_final_result(result, output_file)
        
        return result
    
    def _improve_code(self, current_code: GeneratedCode, test_report: CodeTestReport, 
                     task_description: str) -> Optional[GeneratedCode]:
        """تحسين الكود بناءً على نتائج الاختبار"""
        
        if not test_report.recommendations:
            return None
        
        # إنشاء prompt للتحسين
        improvement_prompt = f"""
المهمة الأصلية: {task_description}

الكود الحالي:
```python
{current_code.code}
```

مشاكل الكود:
{chr(10).join(f"- {rec}" for rec in test_report.recommendations)}

أخطاء الاختبار:
{chr(10).join(f"- {result.test_name}: {result.error}" for result in test_report.test_results if not result.success)}

المطلوب:
1. إصلاح جميع الأخطاء المذكورة
2. تحسين الكود ليجتاز جميع الاختبارات
3. الحفاظ على الوظيفة الأساسية
4. إضافة معالجة للأخطاء إذا لزم الأمر

اكتب الكود المحسن كاملاً:
"""
        
        try:
            # استخدام الوكيل الأساسي للتحسين
            improved_result = self.base_agent.run(improvement_prompt, max_steps=2)
            
            # استخراج الكود المحسن
            improved_code_text = self._extract_code_from_result(str(improved_result))
            
            if improved_code_text and improved_code_text != current_code.code:
                # إنشاء كود محسن جديد
                return GeneratedCode(
                    code=improved_code_text,
                    filename=current_code.filename,
                    description=f"{current_code.description} (محسن)",
                    imports=current_code.imports,
                    functions=current_code.functions,
                    classes=current_code.classes,
                    test_cases=current_code.test_cases,
                    is_executable=True
                )
        
        except Exception as e:
            if self.debug:
                print(f"خطأ في تحسين الكود: {e}")
        
        return None
    
    def _extract_code_from_result(self, result_text: str) -> Optional[str]:
        """استخراج الكود من نتيجة الوكيل"""
        import re
        
        # البحث عن كود Python في النتيجة
        code_patterns = [
            r'```python\n(.*?)\n```',
            r'```\n(.*?)\n```',
            r'def .*?(?=\n\n|\Z)',
        ]
        
        for pattern in code_patterns:
            matches = re.findall(pattern, result_text, re.DOTALL)
            if matches:
                return matches[0].strip()
        
        return None
    
    def _finalize_code(self, code: GeneratedCode, test_report: CodeTestReport) -> str:
        """إنهاء الكود وإضافة التحسينات النهائية"""
        
        final_code = f'''"""
{code.description}

تم إنشاء هذا الكود بواسطة الوكيل الذكي
نتيجة الاختبار: {"نجح" if test_report and test_report.overall_success else "يحتاج تحسين"}
"""

{code.code}
'''
        
        return final_code
    
    def _generate_final_recommendations(self, test_report: CodeTestReport, 
                                      analysis: TaskAnalysis) -> List[str]:
        """توليد التوصيات النهائية"""
        recommendations = []
        
        if test_report:
            if test_report.overall_success:
                recommendations.append("✅ الكود يعمل بشكل صحيح")
                recommendations.append("💡 يمكن إضافة المزيد من التحسينات للأداء")
            else:
                recommendations.extend(test_report.recommendations)
        
        # توصيات حسب نوع المهمة
        if analysis.task_type == "algorithm":
            recommendations.append("🔍 راجع كفاءة الخوارزمية")
        elif analysis.task_type == "file_processing":
            recommendations.append("🔒 تأكد من الأمان في معالجة الملفات")
        
        return recommendations
    
    def _print_final_result(self, result: SolutionResult, output_file: str) -> None:
        """طباعة النتيجة النهائية"""
        
        print("\n" + "="*80)
        print("🏆 النتيجة النهائية")
        print("="*80)
        
        status = "✅ نجح" if result.success else "❌ يحتاج تحسين"
        print(f"📊 حالة الحل: {status}")
        print(f"⏱️ وقت التنفيذ: {result.execution_time:.2f} ثانية")
        print(f"🔄 عدد التكرارات: {result.iterations}")
        print(f"📁 الملف المحفوظ: {output_file}")
        
        if result.test_report:
            print(f"🧪 نتائج الاختبار: {result.test_report.passed_tests}/{result.test_report.total_tests} نجح")
        
        if result.recommendations:
            print("\n💡 التوصيات النهائية:")
            for rec in result.recommendations:
                print(f"  • {rec}")
        
        print("\n🎯 ملخص الحل:")
        print(f"  📋 نوع المهمة: {result.analysis.task_type}")
        print(f"  ⚡ التعقيد: {result.analysis.complexity}")
        print(f"  📦 المكتبات: {', '.join(result.generated_code.imports) if result.generated_code.imports else 'لا توجد'}")
        print(f"  🔧 الدوال: {len(result.generated_code.functions)} دالة")
        
        print("="*80)
        
        if result.success:
            print("🎉 تم حل المشكلة بنجاح! يمكنك تشغيل الكود الآن.")
        else:
            print("⚠️ الحل يحتاج إلى تحسينات إضافية.")
    
    def get_capabilities(self) -> Dict[str, Any]:
        """الحصول على قدرات الوكيل"""
        return {
            "supported_tasks": Config.SUPPORTED_TASK_TYPES,
            "model": self.model_id,
            "max_iterations": self.max_iterations,
            "features": [
                "تحليل المهام الذكي",
                "توليد كود Python",
                "اختبار تلقائي",
                "تحسين مستمر",
                "توقف ذكي",
                "تقارير مفصلة"
            ]
        }
    
    def __str__(self) -> str:
        return f"IntelligentCodingAgent(model={self.model_id}, capabilities={len(self.get_capabilities()['supported_tasks'])} task types)"
