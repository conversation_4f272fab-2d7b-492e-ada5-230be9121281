#!/usr/bin/env python3
"""
عرض توضيحي للوكيل الذكي
"""
import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from intelligent_agent import IntelligentCodingAgent

def demo():
    """عرض توضيحي للوكيل"""
    
    print("🤖 عرض توضيحي للوكيل الذكي")
    print("=" * 50)
    
    # إنشاء الوكيل
    agent = IntelligentCodingAgent()
    
    # مهمة بسيطة للاختبار
    task = "اكتب برنامج لحساب مجموع الأعداد من 1 إلى 10"
    
    print(f"📝 المهمة: {task}")
    print("-" * 50)
    
    # حل المهمة
    result = agent.solve_problem(task, max_iterations=2)
    
    # عرض النتيجة
    print(f"\n🏆 النتيجة: {'✅ نجح' if result.success else '❌ فشل'}")
    
    if result.success:
        print(f"📁 الملف المحفوظ: output/{result.generated_code.filename}")
        print("\n🎉 يمكنك الآن تشغيل الكود!")
    else:
        print("⚠️ الكود يحتاج تحسينات إضافية")

if __name__ == "__main__":
    demo()
