"""
مختبر الكود الذكي - يختبر الكود ويتأكد من صحته
"""
import subprocess
import sys
import os
import tempfile
import time
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from code_generator import GeneratedCode
from config import Config

@dataclass
class TestResult:
    """نتيجة اختبار الكود"""
    success: bool
    output: str
    error: str
    execution_time: float
    test_name: str
    details: Dict[str, Any] = None

@dataclass
class CodeTestReport:
    """تقرير شامل لاختبار الكود"""
    overall_success: bool
    total_tests: int
    passed_tests: int
    failed_tests: int
    execution_time: float
    test_results: List[TestResult]
    recommendations: List[str]

class CodeTester:
    """مختبر الكود الذكي"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.timeout = Config.TEST_TIMEOUT
    
    def test_code(self, generated_code: GeneratedCode) -> CodeTestReport:
        """اختبار شامل للكود المولد"""
        
        print(f"🧪 بدء اختبار الكود: {generated_code.filename}")
        
        test_results = []
        start_time = time.time()
        
        # اختبار 1: فحص الصيغة
        syntax_result = self._test_syntax(generated_code)
        test_results.append(syntax_result)
        
        if syntax_result.success:
            # اختبار 2: التنفيذ الأساسي
            execution_result = self._test_execution(generated_code)
            test_results.append(execution_result)
            
            # اختبار 3: اختبار الدوال الفردية
            if generated_code.functions:
                function_results = self._test_functions(generated_code)
                test_results.extend(function_results)
            
            # اختبار 4: اختبار حالات خاصة
            edge_case_results = self._test_edge_cases(generated_code)
            test_results.extend(edge_case_results)
        
        total_time = time.time() - start_time
        
        # تحليل النتائج
        passed_tests = sum(1 for result in test_results if result.success)
        failed_tests = len(test_results) - passed_tests
        overall_success = failed_tests == 0
        
        # توليد التوصيات
        recommendations = self._generate_recommendations(test_results, generated_code)
        
        return CodeTestReport(
            overall_success=overall_success,
            total_tests=len(test_results),
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            execution_time=total_time,
            test_results=test_results,
            recommendations=recommendations
        )
    
    def _test_syntax(self, generated_code: GeneratedCode) -> TestResult:
        """اختبار صحة الصيغة"""
        start_time = time.time()
        
        try:
            compile(generated_code.code, '<string>', 'exec')
            return TestResult(
                success=True,
                output="الصيغة صحيحة",
                error="",
                execution_time=time.time() - start_time,
                test_name="فحص الصيغة"
            )
        except SyntaxError as e:
            return TestResult(
                success=False,
                output="",
                error=f"خطأ في الصيغة: {str(e)}",
                execution_time=time.time() - start_time,
                test_name="فحص الصيغة"
            )
    
    def _test_execution(self, generated_code: GeneratedCode) -> TestResult:
        """اختبار التنفيذ الأساسي"""
        start_time = time.time()
        
        # حفظ الكود في ملف مؤقت
        temp_file = os.path.join(self.temp_dir, generated_code.filename)
        
        try:
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(generated_code.code)
            
            # تنفيذ الكود
            result = subprocess.run(
                [sys.executable, temp_file],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=self.temp_dir
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                return TestResult(
                    success=True,
                    output=result.stdout,
                    error=result.stderr,
                    execution_time=execution_time,
                    test_name="التنفيذ الأساسي"
                )
            else:
                return TestResult(
                    success=False,
                    output=result.stdout,
                    error=result.stderr,
                    execution_time=execution_time,
                    test_name="التنفيذ الأساسي"
                )
        
        except subprocess.TimeoutExpired:
            return TestResult(
                success=False,
                output="",
                error=f"انتهت مهلة التنفيذ ({self.timeout} ثانية)",
                execution_time=self.timeout,
                test_name="التنفيذ الأساسي"
            )
        except Exception as e:
            return TestResult(
                success=False,
                output="",
                error=f"خطأ في التنفيذ: {str(e)}",
                execution_time=time.time() - start_time,
                test_name="التنفيذ الأساسي"
            )
    
    def _test_functions(self, generated_code: GeneratedCode) -> List[TestResult]:
        """اختبار الدوال الفردية"""
        results = []
        
        for function_name in generated_code.functions:
            if function_name == "main" or function_name.startswith("_"):
                continue
            
            start_time = time.time()
            
            # إنشاء كود اختبار للدالة
            test_code = self._create_function_test(generated_code.code, function_name)
            
            if test_code:
                temp_file = os.path.join(self.temp_dir, f"test_{function_name}.py")
                
                try:
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        f.write(test_code)
                    
                    result = subprocess.run(
                        [sys.executable, temp_file],
                        capture_output=True,
                        text=True,
                        timeout=self.timeout,
                        cwd=self.temp_dir
                    )
                    
                    execution_time = time.time() - start_time
                    
                    results.append(TestResult(
                        success=result.returncode == 0,
                        output=result.stdout,
                        error=result.stderr,
                        execution_time=execution_time,
                        test_name=f"اختبار دالة {function_name}"
                    ))
                
                except Exception as e:
                    results.append(TestResult(
                        success=False,
                        output="",
                        error=f"خطأ في اختبار الدالة: {str(e)}",
                        execution_time=time.time() - start_time,
                        test_name=f"اختبار دالة {function_name}"
                    ))
        
        return results
    
    def _test_edge_cases(self, generated_code: GeneratedCode) -> List[TestResult]:
        """اختبار الحالات الاستثنائية"""
        results = []
        
        # اختبار مع مدخلات فارغة أو خاطئة
        edge_cases = [
            ("مدخلات فارغة", ""),
            ("مدخلات خاطئة", "invalid_input"),
        ]
        
        for case_name, test_input in edge_cases:
            start_time = time.time()
            
            # إنشاء كود اختبار للحالة
            test_code = self._create_edge_case_test(generated_code.code, test_input)
            
            if test_code:
                temp_file = os.path.join(self.temp_dir, f"test_edge_{case_name.replace(' ', '_')}.py")
                
                try:
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        f.write(test_code)
                    
                    result = subprocess.run(
                        [sys.executable, temp_file],
                        capture_output=True,
                        text=True,
                        timeout=self.timeout,
                        cwd=self.temp_dir
                    )
                    
                    execution_time = time.time() - start_time
                    
                    # للحالات الاستثنائية، النجاح يعني عدم تعطل البرنامج
                    success = result.returncode == 0 or "error" not in result.stderr.lower()
                    
                    results.append(TestResult(
                        success=success,
                        output=result.stdout,
                        error=result.stderr,
                        execution_time=execution_time,
                        test_name=f"اختبار {case_name}"
                    ))
                
                except Exception as e:
                    results.append(TestResult(
                        success=False,
                        output="",
                        error=f"خطأ في اختبار الحالة: {str(e)}",
                        execution_time=time.time() - start_time,
                        test_name=f"اختبار {case_name}"
                    ))
        
        return results
    
    def _create_function_test(self, original_code: str, function_name: str) -> str:
        """إنشاء كود اختبار لدالة معينة"""
        
        # استخراج تعريف الدالة
        function_pattern = rf'def {function_name}\([^)]*\):[^def]*?(?=def|\Z)'
        function_match = re.search(function_pattern, original_code, re.DOTALL)
        
        if not function_match:
            return ""
        
        # إنشاء كود الاختبار
        test_code = f"""
{original_code}

# اختبار الدالة {function_name}
try:
    # محاولة استدعاء الدالة مع قيم تجريبية
    if '{function_name}' in globals():
        func = globals()['{function_name}']
        
        # تجربة استدعاءات مختلفة حسب نوع الدالة
        if 'prime' in '{function_name}'.lower():
            result = func(7)
            print(f"نتيجة اختبار {{func.__name__}}(7): {{result}}")
        elif 'sum' in '{function_name}'.lower():
            result = func([1, 2, 3, 4, 5])
            print(f"نتيجة اختبار {{func.__name__}}([1,2,3,4,5]): {{result}}")
        else:
            # محاولة عامة
            result = func()
            print(f"نتيجة اختبار {{func.__name__}}(): {{result}}")
        
        print("✅ اختبار الدالة نجح")
    else:
        print("❌ الدالة غير موجودة")

except Exception as e:
    print(f"❌ خطأ في اختبار الدالة: {{e}}")
"""
        
        return test_code
    
    def _create_edge_case_test(self, original_code: str, test_input: str) -> str:
        """إنشاء كود اختبار للحالات الاستثنائية"""
        
        test_code = f"""
{original_code}

# اختبار الحالات الاستثنائية
try:
    # محاولة تنفيذ الكود مع مدخلات استثنائية
    print("بدء اختبار الحالة الاستثنائية")
    
    # تنفيذ الكود الأساسي
    if 'main' in globals():
        main()
    
    print("✅ اختبار الحالة الاستثنائية نجح")

except Exception as e:
    print(f"⚠️ تم التعامل مع الاستثناء: {{e}}")
"""
        
        return test_code
    
    def _generate_recommendations(self, test_results: List[TestResult], generated_code: GeneratedCode) -> List[str]:
        """توليد توصيات لتحسين الكود"""
        recommendations = []
        
        # تحليل نتائج الاختبارات
        failed_tests = [result for result in test_results if not result.success]
        
        if failed_tests:
            recommendations.append("يحتاج الكود إلى إصلاحات في الأجزاء التي فشلت في الاختبار")
            
            for failed_test in failed_tests:
                if "صيغة" in failed_test.test_name:
                    recommendations.append("إصلاح أخطاء الصيغة في الكود")
                elif "تنفيذ" in failed_test.test_name:
                    recommendations.append("إصلاح أخطاء التنفيذ والمنطق")
                elif "دالة" in failed_test.test_name:
                    recommendations.append(f"مراجعة وإصلاح {failed_test.test_name}")
        
        # توصيات عامة
        if not generated_code.functions:
            recommendations.append("إضافة دوال لتنظيم الكود بشكل أفضل")
        
        if len(generated_code.code.split('\n')) > 100:
            recommendations.append("تقسيم الكود إلى دوال أصغر لسهولة الصيانة")
        
        if not any("try" in line for line in generated_code.code.split('\n')):
            recommendations.append("إضافة معالجة للأخطاء (try-except)")
        
        return recommendations
    
    def print_test_report(self, report: CodeTestReport) -> None:
        """طباعة تقرير الاختبار بشكل منسق"""
        print("\n" + "="*60)
        print("🧪 تقرير اختبار الكود")
        print("="*60)
        
        status = "✅ نجح" if report.overall_success else "❌ فشل"
        print(f"📊 النتيجة العامة: {status}")
        print(f"📈 الاختبارات الناجحة: {report.passed_tests}/{report.total_tests}")
        print(f"⏱️ وقت التنفيذ: {report.execution_time:.2f} ثانية")
        
        print("\n📋 تفاصيل الاختبارات:")
        for result in report.test_results:
            status_icon = "✅" if result.success else "❌"
            print(f"  {status_icon} {result.test_name} ({result.execution_time:.2f}s)")
            if result.output:
                print(f"    📤 المخرجات: {result.output[:100]}...")
            if result.error:
                print(f"    ⚠️ الأخطاء: {result.error[:100]}...")
        
        if report.recommendations:
            print("\n💡 التوصيات:")
            for rec in report.recommendations:
                print(f"  • {rec}")
        
        print("="*60)
    
    def __del__(self):
        """تنظيف الملفات المؤقتة"""
        import shutil
        try:
            shutil.rmtree(self.temp_dir)
        except:
            pass
