"""
إعدادات المشروع الرئيسية
"""
import os
from typing import Dict, Any

class Config:
    """إعدادات الوكيل الذكي"""
    
    # إعدادات النموذج
    MODEL_ID = "gemma3n:latest"
    API_BASE = "http://localhost:11434/v1"
    API_KEY = "ollama"
    
    # إعدادات الوكيل
    MAX_STEPS = 10
    MAX_RETRIES = 3
    TIMEOUT_SECONDS = 300
    
    # إعدادات الاختبار
    TEST_TIMEOUT = 30
    MAX_TEST_RUNS = 5
    
    # مجلدات العمل
    WORKSPACE_DIR = "workspace"
    OUTPUT_DIR = "output"
    TEMP_DIR = "temp"
    
    # أنماط التوقف الذكي
    SUCCESS_PATTERNS = [
        r"test.*passed",
        r"all tests.*passed",
        r"success",
        r"completed successfully",
        r"✅",
        r"تم بنجاح",
        r"اكتمل",
    ]
    
    # أنماط الأخطاء
    ERROR_PATTERNS = [
        r"error",
        r"exception",
        r"failed",
        r"traceback",
        r"❌",
        r"خطأ",
        r"فشل",
    ]
    
    # أنواع المهام المدعومة
    SUPPORTED_TASK_TYPES = [
        "algorithm",      # خوارزميات
        "data_structure", # هياكل البيانات
        "web_scraping",   # استخراج البيانات
        "file_processing", # معالجة الملفات
        "math_calculation", # حسابات رياضية
        "api_integration", # تكامل APIs
        "database",       # قواعد البيانات
        "gui",           # واجهات المستخدم
        "automation",    # أتمتة المهام
        "general",       # عام
    ]
    
    @classmethod
    def get_workspace_path(cls, filename: str = "") -> str:
        """الحصول على مسار مجلد العمل"""
        os.makedirs(cls.WORKSPACE_DIR, exist_ok=True)
        return os.path.join(cls.WORKSPACE_DIR, filename)
    
    @classmethod
    def get_output_path(cls, filename: str = "") -> str:
        """الحصول على مسار مجلد الإخراج"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        return os.path.join(cls.OUTPUT_DIR, filename)
    
    @classmethod
    def get_temp_path(cls, filename: str = "") -> str:
        """الحصول على مسار مجلد المؤقت"""
        os.makedirs(cls.TEMP_DIR, exist_ok=True)
        return os.path.join(cls.TEMP_DIR, filename)

# إعدادات إضافية للبيئات المختلفة
DEVELOPMENT_CONFIG = {
    "debug": True,
    "verbose": True,
    "save_intermediate_results": True,
}

PRODUCTION_CONFIG = {
    "debug": False,
    "verbose": False,
    "save_intermediate_results": False,
}

# اختيار الإعدادات حسب البيئة
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
EXTRA_CONFIG = DEVELOPMENT_CONFIG if ENVIRONMENT == "development" else PRODUCTION_CONFIG
